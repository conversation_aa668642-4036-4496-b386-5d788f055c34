<template>
  <div class="duplicates-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">重复告警管理</h2>
      <div class="header-actions">
        <el-button
          @click="refreshData"
          :loading="loading"
          :icon="Refresh"
        >
          刷新
        </el-button>
        <el-button
          type="primary"
          @click="goToDeduplicationConfig"
          :icon="Setting"
        >
          去重配置
        </el-button>
      </div>
    </div>

    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <template #header>
        <span class="card-title">筛选条件</span>
      </template>
      
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="最小重复次数">
          <el-input-number
            v-model="filterForm.min_duplicates"
            :min="2"
            :max="100"
            style="width: 150px;"
          />
        </el-form-item>
        
        <el-form-item label="告警等级">
          <el-select v-model="filterForm.alert_level" placeholder="选择等级" clearable style="width: 150px;">
            <el-option label="严重" value="critical" />
            <el-option label="警告" value="warning" />
            <el-option label="信息" value="info" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="项目分类">
          <el-input
            v-model="filterForm.project_category"
            placeholder="项目分类"
            clearable
            style="width: 150px;"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="searchDuplicates" :loading="loading">
            搜索
          </el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <div class="stats-cards" v-if="statsData">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-value">{{ statsData.total_duplicate_groups || 0 }}</div>
          <div class="stat-label">重复告警组</div>
        </div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-value">{{ statsData.total_duplicates || 0 }}</div>
          <div class="stat-label">总重复次数</div>
        </div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-value">{{ calculateDeduplicationRate() }}%</div>
          <div class="stat-label">去重率</div>
        </div>
      </el-card>
    </div>

    <!-- 重复告警列表 -->
    <el-card class="duplicates-list-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">重复告警列表</span>
          <div class="header-actions">
            <el-button
              type="success"
              @click="batchResolveSelected"
              :loading="batchLoading"
              :disabled="selectedAlerts.length === 0"
              :icon="Check"
            >
              批量解决 ({{ selectedAlerts.length }})
            </el-button>
          </div>
        </div>
      </template>

      <div v-loading="loading" class="table-container">
        <el-table
          :data="duplicatesList"
          style="width: 100%"
          @selection-change="handleSelectionChange"
          stripe
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column prop="alert_title" label="告警标题" min-width="200">
            <template #default="scope">
              <div class="alert-title-cell">
                <span :title="scope.row.alert_title">{{ scope.row.alert_title }}</span>
                <el-tag
                  v-if="scope.row.duplicate_count > 10"
                  type="danger"
                  size="small"
                  class="high-duplicate-tag"
                >
                  高频
                </el-tag>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="project_category" label="项目分类" width="120" />
          
          <el-table-column prop="alert_level" label="告警等级" width="100">
            <template #default="scope">
              <el-tag :type="getLevelType(scope.row.alert_level)" size="small">
                {{ getLevelText(scope.row.alert_level) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="duplicate_count" label="重复次数" width="120" sortable>
            <template #default="scope">
              <el-badge
                :value="scope.row.duplicate_count"
                :max="99"
                type="warning"
                class="duplicate-count-badge"
              >
                <el-icon class="duplicate-icon">
                  <CopyDocument />
                </el-icon>
              </el-badge>
            </template>
          </el-table-column>
          
          <el-table-column prop="first_occurrence" label="首次出现" width="150">
            <template #default="scope">
              {{ formatDateTime(scope.row.first_occurrence) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="last_occurrence" label="最后出现" width="150">
            <template #default="scope">
              <span class="last-occurrence">
                {{ formatRelativeTime(scope.row.last_occurrence) }}
              </span>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                @click="viewAlertDetail(scope.row.id)"
              >
                查看详情
              </el-button>
              <el-button
                v-if="scope.row.status === 'active'"
                type="success"
                size="small"
                @click="resolveAlert(scope.row.id)"
                :loading="resolvingIds.includes(scope.row.id)"
              >
                解决
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-area" v-if="duplicatesList.length > 0">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Setting,
  Check,
  CopyDocument
} from '@element-plus/icons-vue'
import {
  getDuplicateAlerts,
  getAlertStats,
  resolveAlert as resolveAlertAPI,
  batchResolveAlerts
} from '../api/alertmanager'

export default {
  name: 'AlertManagerDuplicates',
  components: {
    Refresh,
    Setting,
    Check,
    CopyDocument
  },
  setup() {
    const router = useRouter()
    
    // 响应式数据
    const loading = ref(false)
    const batchLoading = ref(false)
    const resolvingIds = ref([])
    
    // 筛选表单
    const filterForm = ref({
      min_duplicates: 2,
      alert_level: '',
      project_category: ''
    })
    
    // 分页
    const currentPage = ref(1)
    const pageSize = ref(20)
    const total = ref(0)
    
    // 数据
    const duplicatesList = ref([])
    const statsData = ref({})
    const selectedAlerts = ref([])

    // 搜索重复告警
    const searchDuplicates = async () => {
      loading.value = true
      try {
        const params = {
          page: currentPage.value,
          per_page: pageSize.value,
          min_duplicates: filterForm.value.min_duplicates
        }

        if (filterForm.value.alert_level) params.alert_level = filterForm.value.alert_level
        if (filterForm.value.project_category) params.project_category = filterForm.value.project_category

        console.log('正在请求重复告警数据...', params)
        const response = await getDuplicateAlerts(params)

        if (response.data.success) {
          duplicatesList.value = response.data.data.items || []
          total.value = response.data.data.total || 0
          console.log('重复告警数据加载成功:', duplicatesList.value)
          ElMessage.success('查询成功')
        } else {
          console.error('API返回错误:', response.data)
          ElMessage.error(response.data.message || '查询失败')
        }
      } catch (error) {
        console.error('请求重复告警数据失败:', error)
        // 如果API不存在，显示模拟数据
        if (error.response?.status === 404) {
          ElMessage.warning('重复告警API尚未实现，显示模拟数据')
          duplicatesList.value = []
          total.value = 0
        } else {
          ElMessage.error('查询失败: ' + (error.response?.data?.message || error.message))
        }
      } finally {
        loading.value = false
      }
    }

    // 加载统计数据
    const loadStats = async () => {
      try {
        console.log('正在加载统计数据...')
        const response = await getAlertStats()
        if (response.data.success) {
          statsData.value = response.data.data
          console.log('统计数据加载成功:', statsData.value)
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
        // 如果统计API失败，设置默认值
        statsData.value = {
          total_duplicate_groups: 0,
          total_duplicates: 0,
          duplicate_alerts: 0
        }
      }
    }

    // 计算去重率
    const calculateDeduplicationRate = () => {
      if (!statsData.value.total_alerts || !statsData.value.total_duplicates) return 0
      const rate = ((statsData.value.total_duplicates - statsData.value.duplicate_alerts) / statsData.value.total_duplicates * 100)
      return Math.round(rate * 100) / 100
    }

    // 重置筛选
    const resetFilter = () => {
      filterForm.value = {
        min_duplicates: 2,
        alert_level: '',
        project_category: ''
      }
      currentPage.value = 1
      searchDuplicates()
    }

    // 刷新数据
    const refreshData = () => {
      searchDuplicates()
      loadStats()
    }

    // 选择变化
    const handleSelectionChange = (selection) => {
      selectedAlerts.value = selection.map(item => item.id)
    }

    // 批量解决告警
    const batchResolveSelected = async () => {
      if (selectedAlerts.value.length === 0) {
        ElMessage.warning('请先选择要解决的告警')
        return
      }

      try {
        await ElMessageBox.confirm(
          `确定要批量解决选中的 ${selectedAlerts.value.length} 个告警吗？`,
          '批量解决确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        batchLoading.value = true
        const response = await batchResolveAlerts({ alert_ids: selectedAlerts.value })

        if (response.data.success) {
          ElMessage.success('批量解决成功')
          selectedAlerts.value = []
          refreshData()
        } else {
          ElMessage.error(response.data.message || '批量解决失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('批量解决失败: ' + (error.response?.data?.message || error.message))
        }
      } finally {
        batchLoading.value = false
      }
    }

    // 解决单个告警
    const resolveAlert = async (alertId) => {
      try {
        await ElMessageBox.confirm('确定要解决这个告警吗？', '解决确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        resolvingIds.value.push(alertId)
        const response = await resolveAlertAPI(alertId)

        if (response.data.success) {
          ElMessage.success('告警已解决')
          refreshData()
        } else {
          ElMessage.error(response.data.message || '解决失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('解决失败: ' + (error.response?.data?.message || error.message))
        }
      } finally {
        resolvingIds.value = resolvingIds.value.filter(id => id !== alertId)
      }
    }

    // 查看告警详情
    const viewAlertDetail = (alertId) => {
      router.push(`/alertmanager/detail/${alertId}`)
    }

    // 跳转到去重配置
    const goToDeduplicationConfig = () => {
      router.push('/alertmanager/deduplication')
    }

    // 分页处理
    const handleSizeChange = (size) => {
      pageSize.value = size
      currentPage.value = 1
      searchDuplicates()
    }

    const handleCurrentChange = (page) => {
      currentPage.value = page
      searchDuplicates()
    }

    // 格式化日期时间
    const formatDateTime = (dateString) => {
      if (!dateString) return ''
      return new Date(dateString).toLocaleString()
    }

    // 格式化相对时间
    const formatRelativeTime = (dateString) => {
      if (!dateString) return ''

      const now = new Date()
      const date = new Date(dateString)
      const diffMs = now - date
      const diffMins = Math.floor(diffMs / (1000 * 60))
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

      if (diffMins < 1) return '刚刚'
      if (diffMins < 60) return `${diffMins}分钟前`
      if (diffHours < 24) return `${diffHours}小时前`
      if (diffDays < 7) return `${diffDays}天前`

      return date.toLocaleDateString()
    }

    // 获取等级类型
    const getLevelType = (level) => {
      switch (level) {
        case 'critical':
          return 'danger'
        case 'warning':
          return 'warning'
        case 'info':
          return 'info'
        default:
          return 'info'
      }
    }

    // 获取等级文本
    const getLevelText = (level) => {
      switch (level) {
        case 'critical':
          return '严重'
        case 'warning':
          return '警告'
        case 'info':
          return '信息'
        default:
          return level
      }
    }

    // 初始化
    onMounted(() => {
      console.log('AlertManagerDuplicates 组件已挂载')
      try {
        // 暂时注释掉API调用，先确保页面能正常显示
        // searchDuplicates()
        // loadStats()

        // 设置一些模拟数据
        duplicatesList.value = []
        statsData.value = {
          total_duplicate_groups: 0,
          total_duplicates: 0,
          duplicate_alerts: 0
        }
        loading.value = false
      } catch (error) {
        console.error('初始化失败:', error)
      }
    })

    return {
      loading,
      batchLoading,
      resolvingIds,
      filterForm,
      currentPage,
      pageSize,
      total,
      duplicatesList,
      statsData,
      selectedAlerts,
      searchDuplicates,
      loadStats,
      calculateDeduplicationRate,
      resetFilter,
      refreshData,
      handleSelectionChange,
      batchResolveSelected,
      resolveAlert,
      viewAlertDetail,
      goToDeduplicationConfig,
      handleSizeChange,
      handleCurrentChange,
      formatDateTime,
      formatRelativeTime,
      getLevelType,
      getLevelText
    }
  }
}
</script>

<style scoped>
.duplicates-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.filter-card {
  margin-bottom: 20px;
}

.card-title {
  font-weight: 600;
  color: #303133;
}

.filter-form {
  margin-top: 16px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  padding: 20px;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.duplicates-list-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-container {
  min-height: 400px;
}

.alert-title-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.high-duplicate-tag {
  flex-shrink: 0;
}

.duplicate-count-badge {
  display: flex;
  align-items: center;
  justify-content: center;
}

.duplicate-icon {
  color: #e6a23c;
  font-size: 16px;
}

.last-occurrence {
  color: #909399;
  font-size: 12px;
}

.pagination-area {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .duplicates-container {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .header-actions {
    justify-content: center;
  }

  .filter-form {
    flex-direction: column;
  }

  .filter-form .el-form-item {
    width: 100%;
  }

  .stats-cards {
    grid-template-columns: 1fr;
  }

  .card-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
}
</style>
