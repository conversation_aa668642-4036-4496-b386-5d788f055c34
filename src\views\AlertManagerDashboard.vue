<template>
  <div class="alert-dashboard-container">
    <!-- 页面标题 -->
    <el-card class="header-card">
      <div class="card-header">
        <span class="card-title">告警统计仪表板</span>
        <div class="header-actions">
          <el-button
            @click="loadDashboardData"
            :loading="loading"
            :icon="Refresh"
          >
            刷新数据
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <!-- 仪表板内容 -->
    <div v-else class="dashboard-content">
      <!-- 统计卡片 -->
      <el-row :gutter="24" class="stats-cards">
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <el-card class="stat-card total-alerts">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Bell /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statsData.total_alerts || 0 }}</div>
                <div class="stat-label">总告警数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <el-card class="stat-card active-alerts">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statsData.active_alerts || 0 }}</div>
                <div class="stat-label">活跃告警</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <el-card class="stat-card resolved-alerts">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statsData.resolved_alerts || 0 }}</div>
                <div class="stat-label">已解决</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <el-card class="stat-card today-alerts">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statsData.today_alerts || 0 }}</div>
                <div class="stat-label">今日告警</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 去重统计卡片 -->
      <el-row :gutter="24" class="dedup-stats-row" v-if="statsData.duplicate_alerts !== undefined">
        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-card class="dedup-stats-card">
            <template #header>
              <div class="chart-header">
                <span class="chart-title">去重效果统计</span>
                <el-button
                  type="primary"
                  size="small"
                  @click="goToDuplicates"
                  :icon="CopyDocument"
                >
                  重复告警管理
                </el-button>
              </div>
            </template>

            <el-row :gutter="16" class="dedup-stats-content">
              <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
                <div class="dedup-stat-item">
                  <div class="dedup-stat-value duplicate-alerts">{{ statsData.duplicate_alerts || 0 }}</div>
                  <div class="dedup-stat-label">重复告警数</div>
                </div>
              </el-col>

              <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
                <div class="dedup-stat-item">
                  <div class="dedup-stat-value total-duplicates">{{ statsData.total_duplicates || 0 }}</div>
                  <div class="dedup-stat-label">总重复次数</div>
                </div>
              </el-col>

              <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
                <div class="dedup-stat-item">
                  <div class="dedup-stat-value dedup-rate">{{ calculateDeduplicationRate() }}%</div>
                  <div class="dedup-stat-label">去重率</div>
                </div>
              </el-col>

              <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
                <div class="dedup-stat-item">
                  <div class="dedup-stat-value saved-alerts">{{ calculateSavedAlerts() }}</div>
                  <div class="dedup-stat-label">节省告警数</div>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>

      <!-- 图表区域 -->
      <el-row :gutter="24" class="charts-row">
        <!-- 告警等级分布 -->
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span class="chart-title">告警等级分布</span>
              </div>
            </template>
            <div class="chart-container">
              <div ref="levelChartRef" class="chart" style="height: 300px;"></div>
            </div>
          </el-card>
        </el-col>
        
        <!-- 项目分类分布 -->
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span class="chart-title">项目分类分布</span>
              </div>
            </template>
            <div class="chart-container">
              <div ref="categoryChartRef" class="chart" style="height: 300px;"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 重复告警图表 -->
      <el-row :gutter="24" class="duplicate-charts-row" v-if="statsData.duplicate_alerts !== undefined">
        <!-- 重复告警趋势图 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span class="chart-title">重复告警趋势</span>
              </div>
            </template>
            <div class="chart-container">
              <div ref="duplicateTrendChartRef" class="chart" style="height: 300px;"></div>
            </div>
          </el-card>
        </el-col>

        <!-- 按等级的重复告警分布 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span class="chart-title">重复告警等级分布</span>
              </div>
            </template>
            <div class="chart-container">
              <div ref="duplicateLevelChartRef" class="chart" style="height: 300px;"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 详细统计表格 -->
      <el-card class="table-card">
        <template #header>
          <div class="chart-header">
            <span class="chart-title">详细统计信息</span>
          </div>
        </template>
        
        <el-tabs v-model="activeTab" type="border-card">
          <el-tab-pane label="告警等级统计" name="level">
            <el-table
              :data="statsData.level_stats || []"
              style="width: 100%"
              stripe
            >
              <el-table-column prop="level" label="告警等级" width="150">
                <template #default="scope">
                  <el-tag :type="getLevelType(scope.row.level)" size="small">
                    {{ getLevelText(scope.row.level) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="count" label="数量" />
              <el-table-column label="占比" width="200">
                <template #default="scope">
                  <el-progress
                    :percentage="getPercentage(scope.row.count, statsData.total_alerts)"
                    :color="getLevelColor(scope.row.level)"
                    :show-text="true"
                  />
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          
          <el-tab-pane label="项目分类统计" name="category">
            <el-table
              :data="statsData.category_stats || []"
              style="width: 100%"
              stripe
            >
              <el-table-column prop="category" label="项目分类" width="150" />
              <el-table-column prop="count" label="数量" />
              <el-table-column label="占比" width="200">
                <template #default="scope">
                  <el-progress
                    :percentage="getPercentage(scope.row.count, statsData.total_alerts)"
                    :show-text="true"
                  />
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-card>

      <!-- 快捷操作 -->
      <el-card class="actions-card">
        <template #header>
          <div class="chart-header">
            <span class="chart-title">快捷操作</span>
          </div>
        </template>
        
        <div class="quick-actions">
          <el-button
            type="primary"
            @click="goToAlertList"
            :icon="List"
            size="large"
          >
            查看告警列表
          </el-button>
          <el-button
            type="success"
            @click="goToWebhookConfig"
            :icon="Setting"
            size="large"
          >
            Webhook配置
          </el-button>
          <el-button
            type="warning"
            @click="goToSilenceRules"
            :icon="Mute"
            size="large"
          >
            静默规则管理
          </el-button>
          <el-button
            type="info"
            @click="goToDuplicates"
            :icon="CopyDocument"
            size="large"
          >
            重复告警管理
          </el-button>
          <el-button
            type="primary"
            @click="goToDeduplicationConfig"
            :icon="Setting"
            size="large"
            plain
          >
            去重配置
          </el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  Bell,
  Warning,
  CircleCheck,
  Clock,
  List,
  Setting,
  Mute,
  CopyDocument
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { getAlertStats } from '../api/alertmanager'

export default {
  name: 'AlertManagerDashboard',
  components: {
    Refresh,
    Bell,
    Warning,
    CircleCheck,
    Clock,
    List,
    Setting,
    Mute,
    CopyDocument
  },
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const statsData = ref({})
    const activeTab = ref('level')
    
    // 图表引用
    const levelChartRef = ref(null)
    const categoryChartRef = ref(null)
    const duplicateTrendChartRef = ref(null)
    const duplicateLevelChartRef = ref(null)

    // 图表实例
    let levelChart = null
    let categoryChart = null
    let duplicateTrendChart = null
    let duplicateLevelChart = null

    // 加载仪表板数据
    const loadDashboardData = async () => {
      loading.value = true
      try {
        const response = await getAlertStats()
        
        if (response.data.success) {
          statsData.value = response.data.data
          console.log('仪表板数据加载成功:', statsData.value)
          // 等待DOM更新后初始化图表
          await nextTick()
          console.log('开始初始化图表...')
          initCharts()
        } else {
          console.error('API返回错误:', response.data)
          ElMessage.error(response.data.message || '获取统计数据失败')
        }
      } catch (error) {
        ElMessage.error('获取统计数据失败: ' + (error.response?.data?.message || error.message))
      } finally {
        loading.value = false
      }
    }

    // 初始化图表
    const initCharts = () => {
      console.log('初始化图表，数据:', statsData.value)
      initLevelChart()
      initCategoryChart()
      // 如果有重复告警数据，初始化重复告警图表
      if (statsData.value.duplicate_alerts !== undefined) {
        console.log('初始化重复告警图表...')
        initDuplicateTrendChart()
        initDuplicateLevelChart()
      } else {
        console.log('没有重复告警数据，跳过重复告警图表初始化')
      }
    }

    // 初始化告警等级分布图表
    const initLevelChart = () => {
      console.log('初始化等级图表，DOM元素:', levelChartRef.value, '数据:', statsData.value.level_stats)
      if (!levelChartRef.value || !statsData.value.level_stats) {
        console.log('等级图表初始化失败：DOM元素或数据不存在')
        return
      }

      if (levelChart) {
        levelChart.dispose()
      }

      levelChart = echarts.init(levelChartRef.value)

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '告警等级',
            type: 'pie',
            radius: '50%',
            data: statsData.value.level_stats.map(item => ({
              value: item.count,
              name: getLevelText(item.level),
              itemStyle: {
                color: getLevelColor(item.level)
              }
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }

      levelChart.setOption(option)
    }

    // 初始化项目分类分布图表
    const initCategoryChart = () => {
      console.log('初始化分类图表，DOM元素:', categoryChartRef.value, '数据:', statsData.value.category_stats)
      if (!categoryChartRef.value || !statsData.value.category_stats) {
        console.log('分类图表初始化失败：DOM元素或数据不存在')
        return
      }

      if (categoryChart) {
        categoryChart.dispose()
      }

      categoryChart = echarts.init(categoryChartRef.value)

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: statsData.value.category_stats.map(item => item.category),
          axisTick: {
            alignWithLabel: true
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '告警数量',
            type: 'bar',
            barWidth: '60%',
            data: statsData.value.category_stats.map(item => ({
              value: item.count,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#83bff6' },
                  { offset: 0.5, color: '#188df0' },
                  { offset: 1, color: '#188df0' }
                ])
              }
            }))
          }
        ]
      }

      categoryChart.setOption(option)
    }

    // 初始化重复告警趋势图表
    const initDuplicateTrendChart = () => {
      console.log('初始化重复告警趋势图表，DOM元素:', duplicateTrendChartRef.value)
      if (!duplicateTrendChartRef.value) {
        console.log('重复告警趋势图表初始化失败：DOM元素不存在')
        return
      }

      if (duplicateTrendChart) {
        duplicateTrendChart.dispose()
      }

      duplicateTrendChart = echarts.init(duplicateTrendChartRef.value)

      // 模拟趋势数据（实际应该从API获取）
      const trendData = [
        { date: '2024-01-01', duplicates: 15 },
        { date: '2024-01-02', duplicates: 23 },
        { date: '2024-01-03', duplicates: 18 },
        { date: '2024-01-04', duplicates: 31 },
        { date: '2024-01-05', duplicates: 25 },
        { date: '2024-01-06', duplicates: 19 },
        { date: '2024-01-07', duplicates: 28 }
      ]

      const option = {
        tooltip: {
          trigger: 'axis',
          formatter: '{b}<br/>{a}: {c}'
        },
        xAxis: {
          type: 'category',
          data: trendData.map(item => item.date),
          axisLabel: {
            formatter: function(value) {
              return value.substring(5) // 只显示月-日
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '重复次数'
        },
        series: [
          {
            name: '重复告警',
            type: 'line',
            smooth: true,
            data: trendData.map(item => item.duplicates),
            lineStyle: {
              color: '#e6a23c',
              width: 3
            },
            itemStyle: {
              color: '#e6a23c'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(230, 162, 60, 0.3)' },
                { offset: 1, color: 'rgba(230, 162, 60, 0.1)' }
              ])
            }
          }
        ]
      }

      duplicateTrendChart.setOption(option)
    }

    // 初始化重复告警等级分布图表
    const initDuplicateLevelChart = () => {
      console.log('初始化重复告警等级图表，DOM元素:', duplicateLevelChartRef.value)
      if (!duplicateLevelChartRef.value) {
        console.log('重复告警等级图表初始化失败：DOM元素不存在')
        return
      }

      if (duplicateLevelChart) {
        duplicateLevelChart.dispose()
      }

      duplicateLevelChart = echarts.init(duplicateLevelChartRef.value)

      // 模拟重复告警等级数据（实际应该从API获取）
      const duplicateLevelData = [
        { level: 'critical', count: 8 },
        { level: 'warning', count: 15 },
        { level: 'info', count: 3 }
      ]

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '重复告警等级',
            type: 'pie',
            radius: '50%',
            data: duplicateLevelData.map(item => ({
              value: item.count,
              name: getLevelText(item.level),
              itemStyle: {
                color: getLevelColor(item.level)
              }
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }

      duplicateLevelChart.setOption(option)
    }

    // 工具函数
    const getLevelType = (level) => {
      switch (level) {
        case 'critical':
          return 'danger'
        case 'warning':
          return 'warning'
        case 'info':
          return 'info'
        default:
          return 'info'
      }
    }

    const getLevelText = (level) => {
      switch (level) {
        case 'critical':
          return '严重'
        case 'warning':
          return '警告'
        case 'info':
          return '信息'
        default:
          return '未知'
      }
    }

    const getLevelColor = (level) => {
      switch (level) {
        case 'critical':
          return '#f56c6c'
        case 'warning':
          return '#e6a23c'
        case 'info':
          return '#909399'
        default:
          return '#909399'
      }
    }

    const getPercentage = (count, total) => {
      if (!total || total === 0) return 0
      return Math.round((count / total) * 100)
    }

    // 导航函数
    const goToAlertList = () => {
      router.push('/alertmanager/list')
    }

    const goToWebhookConfig = () => {
      router.push('/alertmanager/webhook-config')
    }

    const goToSilenceRules = () => {
      router.push('/alertmanager/silence-rules')
    }

    const goToDuplicates = () => {
      router.push('/alertmanager/duplicates')
    }

    const goToDeduplicationConfig = () => {
      router.push('/alertmanager/deduplication')
    }

    // 计算去重率
    const calculateDeduplicationRate = () => {
      if (!statsData.value.total_alerts || !statsData.value.total_duplicates) return 0
      const rate = ((statsData.value.total_duplicates - statsData.value.duplicate_alerts) / statsData.value.total_duplicates * 100)
      return Math.round(rate * 100) / 100
    }

    // 计算节省的告警数
    const calculateSavedAlerts = () => {
      if (!statsData.value.total_duplicates || !statsData.value.duplicate_alerts) return 0
      return statsData.value.total_duplicates - statsData.value.duplicate_alerts
    }

    // 窗口大小变化时重新调整图表
    const handleResize = () => {
      if (levelChart) {
        levelChart.resize()
      }
      if (categoryChart) {
        categoryChart.resize()
      }
      if (duplicateTrendChart) {
        duplicateTrendChart.resize()
      }
      if (duplicateLevelChart) {
        duplicateLevelChart.resize()
      }
    }

    // 初始化
    onMounted(() => {
      loadDashboardData()
      window.addEventListener('resize', handleResize)
    })

    // 清理
    onUnmounted(() => {
      if (levelChart) {
        levelChart.dispose()
      }
      if (categoryChart) {
        categoryChart.dispose()
      }
      if (duplicateTrendChart) {
        duplicateTrendChart.dispose()
      }
      if (duplicateLevelChart) {
        duplicateLevelChart.dispose()
      }
      window.removeEventListener('resize', handleResize)
    })

    return {
      loading,
      statsData,
      activeTab,
      levelChartRef,
      categoryChartRef,
      duplicateTrendChartRef,
      duplicateLevelChartRef,
      loadDashboardData,
      getLevelType,
      getLevelText,
      getLevelColor,
      getPercentage,
      goToAlertList,
      goToWebhookConfig,
      goToSilenceRules,
      goToDuplicates,
      goToDeduplicationConfig,
      calculateDeduplicationRate,
      calculateSavedAlerts
    }
  }
}
</script>

<style scoped>
.alert-dashboard-container {
  padding: var(--spacing-lg);
  background: var(--bg-page);
  min-height: calc(100vh - 60px);
}

.header-card {
  margin-bottom: var(--spacing-lg);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.loading-container {
  padding: calc(var(--spacing-xl) + var(--spacing-sm));
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

/* 统计卡片样式 */
.stats-cards {
  margin-bottom: var(--spacing-lg);
}

.stat-card {
  height: 120px;
  border-radius: var(--border-radius-large);
  transition: all var(--transition-base);
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-dark);
}

.stat-card.total-alerts {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stat-card.active-alerts {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.stat-card.resolved-alerts {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.stat-card.today-alerts {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: var(--spacing-lg);
}

.stat-icon {
  font-size: 48px;
  margin-right: var(--spacing-lg);
  opacity: 0.8;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: var(--font-size-xxl);
  font-weight: bold;
  line-height: 1;
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-size-sm);
  opacity: 0.9;
}

/* 图表区域样式 */
.charts-row {
  margin-bottom: var(--spacing-lg);
}

.chart-card, .table-card, .actions-card {
  margin-bottom: var(--spacing-lg);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-title {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
}

.chart-container {
  padding: var(--spacing-md) 0;
}

.chart {
  width: 100%;
}

/* 快捷操作样式 */
.quick-actions {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.quick-actions .el-button {
  flex: 1;
  min-width: 200px;
  height: 60px;
  font-size: var(--font-size-base);
  border-radius: var(--border-radius-large);
  transition: all var(--transition-base);
}

.quick-actions .el-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-dark);
}

/* 表格样式增强 */
.el-table {
  border-radius: var(--border-radius-base);
  overflow: hidden;
}

.el-progress {
  width: 100%;
}

/* 标签页样式 */
.el-tabs {
  margin-top: var(--spacing-md);
}

.el-tabs__content {
  padding: var(--spacing-md) 0;
}

/* 按钮样式增强 */
.el-button {
  border-radius: var(--border-radius-base);
  transition: all var(--transition-base);
}

.el-button--primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border: none;
}

.el-button--success {
  background: linear-gradient(135deg, var(--success-color), #51cf66);
  border: none;
}

.el-button--warning {
  background: linear-gradient(135deg, var(--warning-color), #ffd43b);
  border: none;
}

/* 去重统计样式 */
.dedup-stats-row {
  margin-bottom: var(--spacing-lg);
}

.dedup-stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.dedup-stats-card .chart-title {
  color: white;
}

.dedup-stats-content {
  padding: var(--spacing-lg) 0;
}

.dedup-stat-item {
  text-align: center;
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.dedup-stat-value {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: var(--spacing-sm);
}

.dedup-stat-value.duplicate-alerts {
  color: #ffd93d;
}

.dedup-stat-value.total-duplicates {
  color: #ff6b6b;
}

.dedup-stat-value.dedup-rate {
  color: #4ecdc4;
}

.dedup-stat-value.saved-alerts {
  color: #95e1d3;
}

.dedup-stat-label {
  font-size: var(--font-size-sm);
  color: rgba(255, 255, 255, 0.9);
}

.duplicate-charts-row {
  margin-bottom: var(--spacing-lg);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .alert-dashboard-container {
    padding: var(--spacing-sm);
  }

  .card-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .header-actions {
    flex-direction: column;
    width: 100%;
  }

  .stat-card {
    height: 100px;
  }

  .stat-content {
    padding: var(--spacing-md);
  }

  .stat-icon {
    font-size: 36px;
    margin-right: var(--spacing-md);
  }

  .stat-value {
    font-size: var(--font-size-xl);
  }

  .quick-actions {
    flex-direction: column;
  }

  .quick-actions .el-button {
    min-width: auto;
    width: 100%;
  }

  .chart {
    height: 250px !important;
  }
}

@media (max-width: 480px) {
  .stats-cards .el-col {
    margin-bottom: var(--spacing-md);
  }

  .stat-card {
    height: 80px;
  }

  .stat-content {
    padding: var(--spacing-sm);
  }

  .stat-icon {
    font-size: 24px;
    margin-right: var(--spacing-sm);
  }

  .stat-value {
    font-size: var(--font-size-lg);
  }

  .stat-label {
    font-size: var(--font-size-xs);
  }

  .chart {
    height: 200px !important;
  }

  .quick-actions .el-button {
    height: 50px;
    font-size: var(--font-size-sm);
  }
}
</style>
