<template>
  <div class="deduplication-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">告警去重配置</h2>
      <div class="header-actions">
        <el-button
          @click="loadConfig"
          :loading="loading"
          :icon="Refresh"
        >
          刷新
        </el-button>
        <el-button
          type="primary"
          @click="goToDuplicates"
          :icon="List"
        >
          重复告警管理
        </el-button>
      </div>
    </div>

    <!-- 配置表单 -->
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">去重配置</span>
          <el-switch
            v-model="configForm.enabled"
            active-text="启用去重"
            inactive-text="禁用去重"
            @change="handleEnableChange"
          />
        </div>
      </template>

      <div v-loading="loading" class="config-content">
        <el-form
          ref="configFormRef"
          :model="configForm"
          :rules="configRules"
          label-width="150px"
          class="config-form"
        >
          <el-form-item label="去重时间窗口" prop="deduplication_window_hours">
            <div class="form-item-content">
              <el-input-number
                v-model="configForm.deduplication_window_hours"
                :min="1"
                :max="168"
                :step="1"
                style="width: 200px;"
              />
              <span class="form-help">小时（1-168小时）</span>
            </div>
            <div class="form-description">
              在此时间窗口内，相同指纹的告警将被视为重复告警
            </div>
          </el-form-item>

          <el-form-item label="指纹字段" prop="fingerprint_fields">
            <div class="fingerprint-fields">
              <el-checkbox-group v-model="configForm.fingerprint_fields">
                <el-checkbox label="alert_title">告警标题</el-checkbox>
                <el-checkbox label="alert_description">告警描述</el-checkbox>
                <el-checkbox label="project_category">项目分类</el-checkbox>
                <el-checkbox label="alert_level">告警等级</el-checkbox>
                <el-checkbox label="source_ip">源IP地址</el-checkbox>
                <el-checkbox label="target_ip">目标IP地址</el-checkbox>
              </el-checkbox-group>
            </div>
            <div class="form-description">
              选择用于生成告警指纹的字段，相同指纹的告警将被识别为重复
            </div>
          </el-form-item>

          <el-form-item label="描述最大长度" prop="description_max_length">
            <div class="form-item-content">
              <el-input-number
                v-model="configForm.description_max_length"
                :min="50"
                :max="1000"
                :step="50"
                style="width: 200px;"
              />
              <span class="form-help">字符（50-1000字符）</span>
            </div>
            <div class="form-description">
              告警描述超过此长度时将被截断用于指纹计算
            </div>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              @click="saveConfig"
              :loading="saving"
              size="large"
            >
              保存配置
            </el-button>
            <el-button
              @click="resetConfig"
              size="large"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 配置预览 -->
    <el-card class="preview-card" v-if="configForm.enabled">
      <template #header>
        <span class="card-title">配置预览</span>
      </template>

      <div class="preview-content">
        <div class="preview-section">
          <h4>当前配置摘要</h4>
          <ul class="config-summary">
            <li>
              <strong>去重状态:</strong>
              <el-tag :type="configForm.enabled ? 'success' : 'danger'">
                {{ configForm.enabled ? '已启用' : '已禁用' }}
              </el-tag>
            </li>
            <li>
              <strong>时间窗口:</strong> {{ configForm.deduplication_window_hours }} 小时
            </li>
            <li>
              <strong>指纹字段:</strong> {{ configForm.fingerprint_fields?.join(', ') || '无' }}
            </li>
            <li>
              <strong>描述长度限制:</strong> {{ configForm.description_max_length }} 字符
            </li>
          </ul>
        </div>

        <div class="preview-section">
          <h4>影响范围评估</h4>
          <div class="impact-assessment">
            <el-alert
              title="配置变更将在下次告警接收时生效"
              type="info"
              :closable="false"
              show-icon
            />
            <div class="impact-details">
              <p>• 现有告警不会受到配置变更影响</p>
              <p>• 新接收的告警将按照新配置进行去重处理</p>
              <p>• 建议在业务低峰期进行配置变更</p>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 统计信息 -->
    <el-card class="stats-card" v-if="statsData">
      <template #header>
        <span class="card-title">去重效果统计</span>
      </template>

      <div class="stats-content">
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ statsData.duplicate_alerts || 0 }}</div>
            <div class="stat-label">重复告警数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ statsData.total_duplicates || 0 }}</div>
            <div class="stat-label">总重复次数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ calculateDeduplicationRate() }}%</div>
            <div class="stat-label">去重率</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ calculateSavedAlerts() }}</div>
            <div class="stat-label">节省告警数</div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  List
} from '@element-plus/icons-vue'
import { 
  getDeduplicationConfig,
  updateDeduplicationConfig,
  getAlertStats
} from '../api/alertmanager'

export default {
  name: 'AlertManagerDeduplication',
  components: {
    Refresh,
    List
  },
  setup() {
    const router = useRouter()
    
    // 响应式数据
    const loading = ref(false)
    const saving = ref(false)
    const configFormRef = ref(null)
    const statsData = ref({})
    
    // 配置表单
    const configForm = reactive({
      enabled: true,
      deduplication_window_hours: 24,
      fingerprint_fields: ['alert_title', 'alert_description', 'project_category'],
      description_max_length: 200
    })

    // 原始配置（用于重置）
    const originalConfig = ref({})

    // 表单验证规则
    const configRules = {
      deduplication_window_hours: [
        { required: true, message: '请设置去重时间窗口', trigger: 'blur' },
        { type: 'number', min: 1, max: 168, message: '时间窗口必须在1-168小时之间', trigger: 'blur' }
      ],
      fingerprint_fields: [
        { required: true, message: '请至少选择一个指纹字段', trigger: 'change' }
      ],
      description_max_length: [
        { required: true, message: '请设置描述最大长度', trigger: 'blur' },
        { type: 'number', min: 50, max: 1000, message: '描述长度必须在50-1000字符之间', trigger: 'blur' }
      ]
    }

    // 加载配置
    const loadConfig = async () => {
      loading.value = true
      try {
        const response = await getDeduplicationConfig()
        
        if (response.data.success) {
          const config = response.data.data
          Object.assign(configForm, config)
          originalConfig.value = { ...config }
          ElMessage.success('配置加载成功')
        } else {
          ElMessage.error(response.data.message || '加载配置失败')
        }
      } catch (error) {
        ElMessage.error('加载配置失败: ' + (error.response?.data?.message || error.message))
      } finally {
        loading.value = false
      }
    }

    // 加载统计数据
    const loadStats = async () => {
      try {
        const response = await getAlertStats()
        if (response.data.success) {
          statsData.value = response.data.data
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
      }
    }

    // 保存配置
    const saveConfig = async () => {
      try {
        const valid = await configFormRef.value.validate()
        if (!valid) return

        saving.value = true
        const response = await updateDeduplicationConfig({
          enabled: configForm.enabled,
          deduplication_window_hours: configForm.deduplication_window_hours,
          fingerprint_fields: configForm.fingerprint_fields,
          description_max_length: configForm.description_max_length
        })

        if (response.data.success) {
          ElMessage.success('配置保存成功')
          originalConfig.value = { ...configForm }
          loadStats() // 重新加载统计数据
        } else {
          ElMessage.error(response.data.message || '保存配置失败')
        }
      } catch (error) {
        ElMessage.error('保存配置失败: ' + (error.response?.data?.message || error.message))
      } finally {
        saving.value = false
      }
    }

    // 重置配置
    const resetConfig = () => {
      Object.assign(configForm, originalConfig.value)
      ElMessage.info('配置已重置')
    }

    // 启用状态变更处理
    const handleEnableChange = (enabled) => {
      if (!enabled) {
        ElMessageBox.confirm(
          '禁用去重功能后，所有新接收的告警都不会进行去重处理，确定要禁用吗？',
          '禁用确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).catch(() => {
          configForm.enabled = true
        })
      }
    }

    // 跳转到重复告警管理
    const goToDuplicates = () => {
      router.push('/alertmanager/duplicates')
    }

    // 计算去重率
    const calculateDeduplicationRate = () => {
      if (!statsData.value.total_alerts || !statsData.value.total_duplicates) return 0
      const rate = ((statsData.value.total_duplicates - statsData.value.duplicate_alerts) / statsData.value.total_duplicates * 100)
      return Math.round(rate * 100) / 100
    }

    // 计算节省的告警数
    const calculateSavedAlerts = () => {
      if (!statsData.value.total_duplicates || !statsData.value.duplicate_alerts) return 0
      return statsData.value.total_duplicates - statsData.value.duplicate_alerts
    }

    // 初始化
    onMounted(() => {
      loadConfig()
      loadStats()
    })

    return {
      loading,
      saving,
      configFormRef,
      statsData,
      configForm,
      configRules,
      loadConfig,
      loadStats,
      saveConfig,
      resetConfig,
      handleEnableChange,
      goToDuplicates,
      calculateDeduplicationRate,
      calculateSavedAlerts
    }
  }
}
</script>

<style scoped>
.deduplication-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.config-card,
.preview-card,
.stats-card {
  margin-bottom: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-weight: 600;
  color: #303133;
}

.config-content {
  padding: 20px 0;
}

.config-form {
  max-width: 800px;
}

.form-item-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.form-help {
  color: #909399;
  font-size: 12px;
}

.form-description {
  margin-top: 8px;
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
}

.fingerprint-fields {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.fingerprint-fields .el-checkbox {
  margin-right: 0;
}

.preview-content {
  padding: 20px 0;
}

.preview-section {
  margin-bottom: 24px;
}

.preview-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.config-summary {
  list-style: none;
  padding: 0;
  margin: 0;
}

.config-summary li {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.config-summary li:last-child {
  border-bottom: none;
}

.impact-assessment {
  margin-top: 16px;
}

.impact-details {
  margin-top: 12px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.impact-details p {
  margin: 8px 0;
  color: #606266;
  font-size: 14px;
}

.stats-content {
  padding: 20px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .deduplication-container {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .header-actions {
    justify-content: center;
  }

  .card-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .config-form {
    max-width: 100%;
  }

  .form-item-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .fingerprint-fields {
    width: 100%;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
