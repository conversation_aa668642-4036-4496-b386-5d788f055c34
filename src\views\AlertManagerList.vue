<template>
  <div class="alert-manager-container">
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">告警搜索</span>
          <el-button 
            type="primary" 
            @click="searchAlerts"
            :loading="loading"
            :icon="Search"
          >
            搜索
          </el-button>
        </div>
      </template>
      
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :default-value="defaultDateRange"
            style="width: 300px;"
          />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 150px;">
            <el-option label="活跃" value="active" />
            <el-option label="已解决" value="resolved" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="告警等级">
          <el-select v-model="searchForm.alert_level" placeholder="选择等级" clearable style="width: 150px;">
            <el-option label="P1 (严重)" value="P1" />
            <el-option label="P2 (重要)" value="P2" />
            <el-option label="P3 (一般)" value="P3" />
            <el-option label="严重" value="critical" />
            <el-option label="警告" value="warning" />
            <el-option label="信息" value="info" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="项目分类">
          <el-input
            v-model="searchForm.project_category"
            placeholder="项目分类"
            clearable
            style="width: 150px;"
          />
        </el-form-item>
        
        <el-form-item label="负责人">
          <el-input
            v-model="searchForm.project_owner"
            placeholder="负责人"
            clearable
            style="width: 150px;"
          />
        </el-form-item>

        <el-form-item label="重复告警">
          <el-select v-model="searchForm.show_duplicates" placeholder="筛选重复告警" clearable style="width: 150px;">
            <el-option label="仅重复告警" value="true" />
            <el-option label="非重复告警" value="false" />
          </el-select>
        </el-form-item>

        <el-form-item label="最小重复次数">
          <el-input-number
            v-model="searchForm.min_duplicates"
            :min="2"
            :max="100"
            placeholder="最小重复次数"
            style="width: 150px;"
          />
        </el-form-item>

        <el-form-item>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计信息 -->
    <el-card class="summary-card" v-if="statsData">
      <div class="summary-info">
        <div class="summary-item">
          <span class="label">总告警数:</span>
          <span class="value">{{ statsData.total_alerts }}</span>
        </div>
        <div class="summary-item">
          <span class="label">活跃告警:</span>
          <span class="value active">{{ statsData.active_alerts }}</span>
        </div>
        <div class="summary-item">
          <span class="label">已解决:</span>
          <span class="value resolved">{{ statsData.resolved_alerts }}</span>
        </div>
        <div class="summary-item">
          <span class="label">今日告警:</span>
          <span class="value today">{{ statsData.today_alerts }}</span>
        </div>
        <div class="summary-item" v-if="statsData.duplicate_alerts !== undefined">
          <span class="label">重复告警:</span>
          <span class="value duplicate">{{ statsData.duplicate_alerts }}</span>
        </div>
        <div class="summary-item" v-if="statsData.total_duplicates !== undefined">
          <span class="label">总重复次数:</span>
          <span class="value total-duplicates">{{ statsData.total_duplicates }}</span>
        </div>
      </div>
    </el-card>

    <!-- 告警列表 -->
    <el-card class="alert-list-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">告警列表</span>
          <div class="header-actions">
            <div class="selection-actions" v-if="activeAlerts.length > 0">
              <el-checkbox
                v-model="selectAll"
                @change="handleSelectAll"
                :indeterminate="isIndeterminate"
              >
                全选
              </el-checkbox>
              <span class="selection-info">
                已选择 {{ selectedAlerts.length }} / {{ activeAlerts.length }} 个活跃告警
              </span>
            </div>
            <div class="action-buttons">
              <el-button
                type="success"
                @click="batchResolveSelected"
                :loading="batchLoading"
                :disabled="selectedAlerts.length === 0"
                :icon="Check"
              >
                批量解决 ({{ selectedAlerts.length }})
              </el-button>
              <el-button
                @click="refreshData"
                :loading="loading"
                :icon="Refresh"
              >
                刷新
              </el-button>
            </div>
          </div>
        </div>
      </template>

      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="8" animated />
      </div>

      <div v-else-if="alertList.length === 0" class="empty-container">
        <el-empty description="暂无告警数据" />
      </div>

      <div v-else class="alert-cards-container">
        <el-row :gutter="16">
          <el-col
            v-for="alert in alertList"
            :key="alert.id"
            :xs="24"
            :sm="12"
            :md="12"
            :lg="8"
            :xl="6"
            class="alert-card-col"
          >
            <div
              class="alert-status-card"
              :class="getAlertCardClass(alert.status, alert.alert_level)"
            >
              <div class="card-header">
                <div class="alert-info">
                  <div class="alert-title-row">
                    <span class="alert-id">告警 #{{ alert.id }}</span>
                    <el-checkbox
                      v-if="alert.status === 'active'"
                      v-model="selectedAlerts"
                      :value="alert.id"
                      class="alert-checkbox"
                      @change="handleAlertSelect"
                    />
                  </div>
                  <el-tag
                    :type="getStatusType(alert.status)"
                    size="large"
                    class="status-tag"
                  >
                    <el-icon class="status-icon">
                      <component :is="getStatusIcon(alert.status)" />
                    </el-icon>
                    {{ getStatusText(alert.status) }}
                  </el-tag>
                  <el-tag
                    :type="getLevelType(alert.alert_level)"
                    size="small"
                    class="level-tag"
                  >
                    {{ getLevelText(alert.alert_level) }}
                  </el-tag>
                  <!-- 重复次数徽章 -->
                  <el-badge
                    v-if="alert.duplicate_count && alert.duplicate_count > 1"
                    :value="alert.duplicate_count"
                    :max="99"
                    class="duplicate-badge"
                    type="warning"
                  >
                    <el-icon class="duplicate-icon">
                      <CopyDocument />
                    </el-icon>
                  </el-badge>
                </div>
                <div class="card-actions">
                  <el-button
                    v-if="alert.status === 'active'"
                    type="success"
                    size="small"
                    @click="resolveAlert(alert.id)"
                    :loading="resolvingIds.includes(alert.id)"
                    class="resolve-button"
                  >
                    标记解决
                  </el-button>
                </div>
              </div>
              
              <div class="card-content">
                <div class="alert-details">
                  <div class="info-row">
                    <span class="label">标题:</span>
                    <span class="value" :title="alert.alert_title">{{ alert.alert_title }}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">描述:</span>
                    <span class="value" :title="alert.alert_description">{{ alert.alert_description }}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">负责人:</span>
                    <span class="value">{{ alert.project_owner }}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">分类:</span>
                    <span class="value">{{ alert.project_category }}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">告警时间:</span>
                    <span class="value">{{ alert.alert_time }}</span>
                  </div>
                  <div class="info-row" v-if="alert.recovery_time">
                    <span class="label">恢复时间:</span>
                    <span class="value">{{ alert.recovery_time }}</span>
                  </div>
                  <div class="info-row" v-if="alert.duplicate_count && alert.duplicate_count > 1">
                    <span class="label">重复次数:</span>
                    <span class="value duplicate-count">{{ alert.duplicate_count }} 次</span>
                  </div>
                  <div class="info-row" v-if="alert.last_occurrence">
                    <span class="label">最后出现:</span>
                    <span class="value">{{ formatRelativeTime(alert.last_occurrence) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 分页 -->
      <div class="pagination-area" v-if="alertList.length > 0">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'

import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Check,
  CircleCheck,
  Clock,
  CircleClose,
  Warning,
  Bell,
  CopyDocument
} from '@element-plus/icons-vue'
import { 
  getAlertLogs, 
  getAlertStats, 
  resolveAlert as resolveAlertApi, 
  batchResolveAlerts 
} from '../api/alertmanager'

export default {
  name: 'AlertManagerList',
  components: {
    Search,
    Refresh,
    Check,
    CircleCheck,
    Clock,
    CircleClose,
    Warning,
    Bell
  },
  setup() {
    const loading = ref(false)
    const batchLoading = ref(false)
    const resolvingIds = ref([])
    
    // 搜索表单
    const searchForm = ref({
      status: '',
      alert_level: '',
      project_category: '',
      project_owner: '',
      show_duplicates: '',
      min_duplicates: null
    })
    
    // 日期范围 - 默认7天内
    const dateRange = ref([])
    const defaultDateRange = computed(() => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 7)
      return [start, end]
    })
    
    // 分页
    const currentPage = ref(1)
    const pageSize = ref(20)
    const total = ref(0)
    
    // 数据
    const alertList = ref([])
    const statsData = ref(null)
    
    // 选择相关
    const selectedAlerts = ref([])
    const selectAll = ref(false)

    // 计算属性
    const activeAlerts = computed(() => {
      return alertList.value.filter(alert => alert.status === 'active')
    })

    const isIndeterminate = computed(() => {
      const selectedCount = selectedAlerts.value.length
      const totalActive = activeAlerts.value.length
      return selectedCount > 0 && selectedCount < totalActive
    })

    // 初始化日期范围
    const initDateRange = () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 7)
      dateRange.value = [
        start.toISOString().split('T')[0],
        end.toISOString().split('T')[0]
      ]
    }

    // 搜索告警
    const searchAlerts = async () => {
      loading.value = true
      try {
        const params = {
          page: currentPage.value,
          per_page: pageSize.value
        }

        // 添加搜索条件
        if (searchForm.value.status) params.status = searchForm.value.status
        if (searchForm.value.alert_level) params.alert_level = searchForm.value.alert_level
        if (searchForm.value.project_category) params.project_category = searchForm.value.project_category
        if (searchForm.value.project_owner) params.project_owner = searchForm.value.project_owner
        if (searchForm.value.show_duplicates) params.show_duplicates = searchForm.value.show_duplicates
        if (searchForm.value.min_duplicates) params.min_duplicates = searchForm.value.min_duplicates

        // 添加日期范围
        if (dateRange.value && dateRange.value.length === 2) {
          params.start_date = dateRange.value[0]
          params.end_date = dateRange.value[1]
        }

        const response = await getAlertLogs(params)

        if (response.data.success) {
          alertList.value = response.data.data.items || []
          total.value = response.data.data.total || 0
          ElMessage.success('查询成功')
        } else {
          ElMessage.error(response.data.message || '查询失败')
        }
      } catch (error) {
        ElMessage.error('查询失败: ' + (error.response?.data?.message || error.message))
      } finally {
        loading.value = false
      }
    }

    // 获取统计数据
    const loadStats = async () => {
      try {
        const response = await getAlertStats()
        if (response.data.success) {
          statsData.value = response.data.data
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
      }
    }

    // 重置搜索
    const resetSearch = () => {
      searchForm.value = {
        status: '',
        alert_level: '',
        project_category: '',
        project_owner: ''
      }
      initDateRange()
      currentPage.value = 1
      searchAlerts()
    }

    // 刷新数据
    const refreshData = () => {
      searchAlerts()
      loadStats()
    }

    // 单个告警解决
    const resolveAlert = async (alertId) => {
      try {
        await ElMessageBox.confirm(
          `确定要标记告警 #${alertId} 为已解决吗？`,
          '确认解决',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        resolvingIds.value.push(alertId)

        const response = await resolveAlertApi(alertId)

        if (response.data.success) {
          ElMessage.success(response.data.message || '告警已标记为解决')
          // 刷新列表
          searchAlerts()
          loadStats()
        } else {
          ElMessage.error(response.data.message || '操作失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('操作失败: ' + (error.response?.data?.message || error.message))
        }
      } finally {
        resolvingIds.value = resolvingIds.value.filter(id => id !== alertId)
      }
    }

    // 处理全选
    const handleSelectAll = (checked) => {
      if (checked) {
        selectedAlerts.value = activeAlerts.value.map(alert => alert.id)
      } else {
        selectedAlerts.value = []
      }
    }

    // 处理单个告警选择
    const handleAlertSelect = () => {
      const totalActive = activeAlerts.value.length
      const selectedCount = selectedAlerts.value.length

      if (selectedCount === 0) {
        selectAll.value = false
      } else if (selectedCount === totalActive) {
        selectAll.value = true
      } else {
        selectAll.value = false
      }
    }

    // 批量解决选中的告警
    const batchResolveSelected = async () => {
      if (selectedAlerts.value.length === 0) {
        ElMessage.warning('请先选择要解决的告警')
        return
      }

      try {
        await ElMessageBox.confirm(
          `确定要批量解决选中的 ${selectedAlerts.value.length} 个告警吗？`,
          '确认批量解决',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        batchLoading.value = true

        const response = await batchResolveAlerts({
          alert_ids: selectedAlerts.value
        })

        if (response.data.success) {
          ElMessage.success(
            `批量解决完成！成功解决 ${response.data.updated_count} 个告警`
          )

          // 清空选择
          selectedAlerts.value = []
          selectAll.value = false

          // 刷新列表
          searchAlerts()
          loadStats()
        } else {
          ElMessage.error(response.data.message || '批量解决失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('批量解决失败: ' + (error.response?.data?.message || error.message))
        }
      } finally {
        batchLoading.value = false
      }
    }



    // 格式化相对时间
    const formatRelativeTime = (dateString) => {
      if (!dateString) return ''

      const now = new Date()
      const date = new Date(dateString)
      const diffMs = now - date
      const diffMins = Math.floor(diffMs / (1000 * 60))
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

      if (diffMins < 1) return '刚刚'
      if (diffMins < 60) return `${diffMins}分钟前`
      if (diffHours < 24) return `${diffHours}小时前`
      if (diffDays < 7) return `${diffDays}天前`

      return date.toLocaleDateString()
    }

    return {
      loading,
      batchLoading,
      resolvingIds,
      searchForm,
      dateRange,
      defaultDateRange,
      currentPage,
      pageSize,
      total,
      alertList,
      statsData,
      selectedAlerts,
      selectAll,
      activeAlerts,
      isIndeterminate,
      initDateRange,
      searchAlerts,
      loadStats,
      resetSearch,
      refreshData,
      resolveAlert,
      handleSelectAll,
      handleAlertSelect,
      batchResolveSelected,
      formatRelativeTime,
      // 工具函数
      getStatusType,
      getStatusIcon,
      getStatusText,
      getLevelType,
      getLevelText,
      getAlertCardClass,
      handleSizeChange,
      handleCurrentChange
    }

    // 获取状态类型
    function getStatusType(status) {
      switch (status) {
        case 'active':
          return 'danger'
        case 'resolved':
          return 'success'
        default:
          return 'info'
      }
    }

    // 获取状态图标
    function getStatusIcon(status) {
      switch (status) {
        case 'active':
          return Bell
        case 'resolved':
          return CircleCheck
        default:
          return Warning
      }
    }

    // 获取状态文本
    function getStatusText(status) {
      switch (status) {
        case 'active':
          return '活跃'
        case 'resolved':
          return '已解决'
        default:
          return '未知'
      }
    }

    // 获取等级类型
    function getLevelType(level) {
      switch (level) {
        case 'P1':
          return 'danger'
        case 'P2':
          return 'warning'
        case 'P3':
          return 'primary'
        case 'critical':
          return 'danger'
        case 'warning':
          return 'warning'
        case 'info':
          return 'info'
        default:
          return 'info'
      }
    }

    // 获取等级文本
    function getLevelText(level) {
      switch (level) {
        case 'P1':
          return 'P1 严重'
        case 'P2':
          return 'P2 重要'
        case 'P3':
          return 'P3 一般'
        case 'critical':
          return '严重'
        case 'warning':
          return '警告'
        case 'info':
          return '信息'
        default:
          return level || '未知'
      }
    }

    // 获取卡片样式类
    function getAlertCardClass(status, level) {
      const statusClass = status === 'active' ? 'active' : 'resolved'
      let levelClass = ''
      if (level) {
        // 将P1、P2等格式转换为CSS类名
        if (level.startsWith('P')) {
          levelClass = `level-${level.toLowerCase()}`
        } else {
          levelClass = `level-${level}`
        }
      }
      return `${statusClass} ${levelClass}`
    }

    // 分页处理
    function handleSizeChange(val) {
      pageSize.value = val
      currentPage.value = 1
      searchAlerts()
    }

    function handleCurrentChange(val) {
      currentPage.value = val
      searchAlerts()
    }

    // 初始化
    onMounted(() => {
      initDateRange()
      searchAlerts()
      loadStats()
    })
  }
}
</script>

<style scoped>
.alert-manager-container {
  padding: var(--spacing-lg);
  background: var(--bg-page);
  min-height: calc(100vh - 60px);
}

.search-card, .summary-card, .alert-list-card {
  margin-bottom: var(--spacing-lg);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
}

.search-form {
  margin-top: var(--spacing-sm);
}

.search-form .el-form-item {
  margin-bottom: var(--spacing-sm);
}

.summary-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.summary-info {
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
}

.summary-item {
  text-align: center;
}

.summary-item .label {
  display: block;
  font-size: var(--font-size-sm);
  opacity: 0.9;
  margin-bottom: var(--spacing-xs);
}

.summary-item .value {
  display: block;
  font-size: var(--font-size-xxl);
  font-weight: bold;
}

.summary-item .value.active {
  color: #ff6b6b;
}

.summary-item .value.resolved {
  color: #51cf66;
}

.summary-item .value.today {
  color: #ffd700;
}

.header-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  align-items: flex-end;
}

.selection-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: var(--font-size-sm);
}

.selection-info {
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
}

.action-buttons {
  display: flex;
  gap: var(--spacing-sm);
}

.loading-container {
  padding: calc(var(--spacing-xl) + var(--spacing-sm));
}

.empty-container {
  padding: calc(var(--spacing-xl) + var(--spacing-sm));
  text-align: center;
}

.alert-cards-container {
  margin-bottom: var(--spacing-lg);
}

.alert-card-col {
  margin-bottom: var(--spacing-md);
}

.alert-status-card {
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-large);
  background: var(--bg-color);
  transition: all var(--transition-base);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.alert-status-card:hover {
  box-shadow: var(--shadow-dark);
  transform: translateY(-2px);
}

.alert-status-card.active {
  border-left: 4px solid var(--danger-color);
}

.alert-status-card.resolved {
  border-left: 4px solid var(--success-color);
  opacity: 0.8;
}

.alert-status-card.level-p1 {
  background: linear-gradient(135deg, rgba(245, 108, 108, 0.08) 0%, rgba(245, 108, 108, 0.03) 100%);
  border-left-color: #f56c6c !important;
}

.alert-status-card.level-p2 {
  background: linear-gradient(135deg, rgba(230, 162, 60, 0.08) 0%, rgba(230, 162, 60, 0.03) 100%);
  border-left-color: #e6a23c !important;
}

.alert-status-card.level-p3 {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.08) 0%, rgba(64, 158, 255, 0.03) 100%);
  border-left-color: #409eff !important;
}

.alert-status-card.level-critical {
  background: linear-gradient(135deg, rgba(245, 108, 108, 0.05) 0%, rgba(245, 108, 108, 0.02) 100%);
}

.alert-status-card.level-warning {
  background: linear-gradient(135deg, rgba(230, 162, 60, 0.05) 0%, rgba(230, 162, 60, 0.02) 100%);
}

.alert-status-card.level-info {
  background: linear-gradient(135deg, rgba(144, 147, 153, 0.05) 0%, rgba(144, 147, 153, 0.02) 100%);
}

.alert-status-card .card-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-lighter);
  flex-direction: column;
  align-items: flex-start;
  gap: var(--spacing-sm);
}

.alert-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  width: 100%;
}

.alert-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.alert-id {
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.alert-checkbox {
  margin-left: var(--spacing-sm);
}

.status-tag, .level-tag {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.level-tag {
  margin-left: var(--spacing-sm);
}

.status-icon {
  font-size: var(--font-size-sm);
}

.card-actions {
  display: flex;
  gap: var(--spacing-sm);
  width: 100%;
  margin-top: var(--spacing-sm);
}

.resolve-button {
  flex: 1;
}

.card-content {
  padding: var(--spacing-md);
  flex: 1;
}

.alert-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.info-row {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  font-size: var(--font-size-xs);
}

.info-row .label {
  color: var(--text-secondary);
  min-width: 60px;
  flex-shrink: 0;
}

.info-row .value {
  color: var(--text-primary);
  word-break: break-all;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.info-row .value.duplicate-count {
  color: #e6a23c;
  font-weight: 600;
}

/* 重复告警样式 */
.duplicate-badge {
  margin-left: var(--spacing-sm);
}

.duplicate-icon {
  color: #e6a23c;
  font-size: var(--font-size-sm);
}

/* 统计信息样式 */
.summary-item .value.duplicate {
  color: #e6a23c;
}

.summary-item .value.total-duplicates {
  color: #f56c6c;
}

.pagination-area {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-lg);
  padding: var(--spacing-lg) 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .alert-manager-container {
    padding: var(--spacing-sm);
  }

  .search-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    width: 100%;
  }

  .summary-info {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .header-actions {
    align-items: stretch;
    width: 100%;
  }

  .selection-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;
  }

  .card-actions {
    flex-direction: column;
  }

  .alert-info {
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .alert-title-row {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .status-tag, .level-tag {
    margin-left: 0;
  }
}
</style>
